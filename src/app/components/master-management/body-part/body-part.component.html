<app-toast-message></app-toast-message>
<div class="card" id="activated-qrcode">
  <div class="card-header d-flex align-items-center justify-content-between">
    <div>
      <h6 class="mb-0">Body Parts</h6>
    </div>
    <div class="d-flex align-items-center">
      <!-- *** REPLACE the existing simple Download button with this Dropdown *** -->
        <div ngbDropdown class="d-inline-block me-2">
          <button type="button" class="btn btn-sm adani-btn dropdown-toggle" id="downloadBPExcelDropdown" ngbDropdownToggle
              [disabled]="isDownloadingExcel || listLoading">
              <span *ngIf="!isDownloadingExcel">
                  <i class="bi bi-file-earmark-excel me-1"></i> Download Excel
              </span>
              <span *ngIf="isDownloadingExcel">
                  <span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
                  Downloading {{ downloadType === 'current' ? 'Page' : (downloadType === 'all' ? 'All' : '') }}...
              </span>
          </button>
          <ul ngbDropdownMenu aria-labelledby="downloadBPExcelDropdown">
              <li>
                  <button ngbDropdownItem (click)="downloadExcel('current')" [disabled]="isDownloadingExcel || listLoading || (bodyPartList?.length ?? 0) === 0">
                      <i class="bi bi-download me-1"></i> Download Current Page ({{ bodyPartList?.length ?? 0 }})
                  </button>
              </li>
              <li>
                  <button ngbDropdownItem (click)="downloadExcel('all')" [disabled]="isDownloadingExcel || listLoading || totalItems === 0">
                      <i class="bi bi-cloud-download me-1"></i> Download All Filtered ({{ totalItems }})
                  </button>
              </li>
          </ul>
      </div>
        <!-- *** NEW: Create Button *** -->
        <button class="btn-sm adani-btn ms-2" (click)="openCreateModal()" title="Create New Body Part">
          <i class="bi bi-plus-circle"></i> Create New
        </button>
        <app-filter-indicator
            [currentFilters]="filters"
            [defaultFilters]="defaultFilters"
            title="Filter Body Parts"
            altText="Filter"
            customClasses="ms-3"
            (filterClick)="openFilterModal()">
        </app-filter-indicator>
      </div>
    </div>
  <div class="card-body">
    <div class="table-container">
      <div class="table-responsive">
        <table class="table table-bordered table-hover">
          <thead class="table-header">
            <tr class="text-center">
              <th scope="col">Id</th>
              <th scope="col">Enabled/Disabled</th>
              <th scope="col">Body Part Name</th> <!-- Changed header -->
              <th scope="col">Actions</th>
            </tr>
          </thead>
          <tbody>
             <!-- Loading Indicator -->
             <tr *ngIf="listLoading">
                <td colspan="4" class="text-center">
                    <div class="spinner-border spinner-border-sm" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    Loading Body Parts...
                </td>
            </tr>
             <!-- No Data Message -->
             <tr *ngIf="!listLoading && bodyPartList.length === 0">
                <td colspan="4" class="text-center">No body parts found.</td>
            </tr>
            <!-- Data Rows -->
            <tr *ngFor="let item of bodyPartList">
              <td class="text-center">{{ item.id }}</td>
              <td>
                <app-switch
                    [(checked)]="item.enabled"
                    [requireConfirmation]="true"
                    (checkedChange)="onSwitchToggle($event, item)"
                    onLabel="Active" offLabel="Inactive">
                </app-switch>
              </td>
              <td>{{ item.title }}</td>
              <td class="actions text-center"> <!-- Added text-center -->
                 <!-- *** UPDATED: Edit Button Click Handler *** -->
                <button class="adani-btn" (click)="openEditModal(item)" title="Edit Body Part">
                  <i class="bi bi-pencil edit"></i>
              </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
  <div class="card-footer text-muted text-center">
    <app-pagination [currentPage]="currentPage" [totalItems]="totalItems" [itemsPerPage]="itemsPerPage"
      (pageChange)="onPageChange($event)"></app-pagination>
  </div>
</div>

<!-- Filter Offcanvas (Renamed *ngIf variable) -->
<app-offcanvas [title]="'Filter Body Parts'" *ngIf="isFilterModalOpen" (onClickCross)="closeFilterModal()">
  <div class="filter-container p-3">
      <form #filterForm="ngForm" (ngSubmit)="applyFilters()">
          <div class="row g-3">

              <div class="col-12">
                  <label class="form-label" for="filterBodyPartName">Body Part Name</label>
                  <input type="text" id="filterBodyPartName" class="form-control" placeholder="Search by Body Part Name"
                         [(ngModel)]="filters.name" name="name" maxlength="30" pattern="^[a-zA-Z ]*$" #filterNameInput="ngModel">
                  <div *ngIf="filterNameInput.invalid && (filterNameInput.dirty || filterNameInput.touched)" class="text-danger small mt-1">
                      <div *ngIf="filterNameInput.errors?.['pattern']">Body part name should contain only alphabets.</div>
                      <div *ngIf="filterNameInput.errors?.['maxlength']">Body part name cannot exceed 30 characters.</div>
                  </div>
                  <div class="text-muted small mt-1" *ngIf="filters.name">
                    {{filters.name.length}}/30 characters
                  </div>
              </div>

               <div class="col-12">
                  <label class="form-label" for="filterEnabledBP">Enabled Status</label>
                  <select id="filterEnabledBP" class="form-select"
                          [(ngModel)]="filters.enabled" name="enabled">
                      <option [ngValue]="null">Any</option>
                      <option value="true">Yes</option>
                      <option value="false">No</option>
                  </select>
              </div>

              <div class="col-12">
                  <label class="form-label" for="filterSortByBP">Sort By</label>
                  <select id="filterSortByBP" class="form-select"
                          [(ngModel)]="filters.sortField" name="sortField">
                      
                      <option *ngFor="let field of availableSortFields" [value]="field.value">{{ field.label }}</option>
                  </select>
                   <label class="form-label mt-2" for="filterSortDirBP">Sort Direction</label>
                   <select id="filterSortDirBP" class="form-select"
                          [(ngModel)]="filters.sortDirection" name="sortDirection">
                      <option value="ASC">Ascending</option>
                      <option value="DESC">Descending</option>
                  </select>
              </div>

              <div class="col-12 mt-4 d-grid gap-2">
                  <button type="submit" class="btn adani-btn">
                      <i class="bi bi-search me-1"></i> Search
                  </button>
                   <button type="button" class="btn btn-secondary" (click)="resetFilters()">
                      <i class="bi bi-arrow-clockwise me-1"></i> Reset Filters
                  </button>
              </div>
          </div>
      </form>
  </div>
</app-offcanvas>

<!-- ********** NEW: Edit Offcanvas ********** -->
<app-offcanvas [title]="'Edit Body Part'" *ngIf="isEditModalOpen" (onClickCross)="closeEditModal()">
  <div class="edit-container p-3">
      <form *ngIf="selectedBodyPart" #editForm="ngForm" (ngSubmit)="submitEditForm()">
          <div class="row g-3">

              <!-- Body Part ID (Readonly) -->
              <div class="col-12">
                  <label class="form-label" for="editBPId">Body Part ID</label>
                  <input type="text" id="editBPId" class="form-control"
                         [value]="selectedBodyPart.id" name="id" readonly disabled>
              </div>

              <!-- Body Part Name (Editable) -->
              <div class="col-12">
                  <label class="form-label" for="editBPName">Body Part Name</label>
                  <input type="text" id="editBPName" class="form-control" placeholder="Enter Body Part Name"
                         [(ngModel)]="selectedBodyPart.title" name="title" required maxlength="30"
                         pattern="^[a-zA-Z ]*$" #titleInput="ngModel">
                   <div *ngIf="titleInput.invalid && (titleInput.dirty || titleInput.touched)" class="text-danger small mt-1">
                      <div *ngIf="titleInput.errors?.['required']">Body part name is required.</div>
                      <div *ngIf="titleInput.errors?.['pattern']">Body part name should contain only alphabets.</div>
                      <div *ngIf="titleInput.errors?.['maxlength']">Body part name cannot exceed 30 characters.</div>
                  </div>
                  <div class="text-muted small mt-1" *ngIf="selectedBodyPart.title">
                    {{selectedBodyPart.title.length}}/30 characters
                  </div>
              </div>

               <!-- Enabled Status -->
               <div class="col-12">
                   <label class="form-label d-block mb-2">Status</label>
                    <app-switch
                          [(checked)]="selectedBodyPart.enabled"
                          name="enabled"
                          onLabel="Active"
                          offLabel="Inactive">
                    </app-switch>
               </div>

              <!-- Action Buttons -->
              <div class="col-12 mt-4 d-flex justify-content-end gap-2">
                  <button type="button" class="btn btn-secondary" (click)="closeEditModal()">
                      Cancel
                  </button>
                  <button type="submit" class="btn adani-btn" [disabled]="editForm.invalid || editLoading">
                       <span *ngIf="!editLoading"><i class="bi bi-save me-1"></i> Save Changes</span>
                       <span *ngIf="editLoading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                       <span *ngIf="editLoading"> Saving...</span>
                  </button>
              </div>
          </div>
      </form>
      <div *ngIf="!selectedBodyPart && isEditModalOpen" class="text-center p-5">
           <div class="spinner-border spinner-border-sm" role="status">
               <span class="visually-hidden">Loading form...</span>
           </div>
      </div>
  </div>
</app-offcanvas>
<!-- ********** END: Edit Offcanvas ********** -->

 <!-- ********** NEW: Create Offcanvas ********** -->
<app-offcanvas [title]="'Create New Body Part'" *ngIf="isCreateModalOpen" (onClickCross)="closeCreateModal()">
  <div class="create-container p-3">
      <form #createForm="ngForm" (ngSubmit)="submitCreateForm()">
          <div class="row g-3">

              <!-- Body Part Name (Required) -->
              <div class="col-12">
                  <label class="form-label" for="createBPName">Body Part Name</label>
                  <input type="text" id="createBPName" class="form-control" placeholder="Enter New Body Part Name"
                         [(ngModel)]="newBodyPartData.title" name="title" required maxlength="30"
                         pattern="^[a-zA-Z ]*$" #createTitleInput="ngModel">
                  <div *ngIf="createTitleInput.invalid && (createTitleInput.dirty || createTitleInput.touched)" class="text-danger small mt-1">
                      <div *ngIf="createTitleInput.errors?.['required']">Body part name is required.</div>
                      <div *ngIf="createTitleInput.errors?.['pattern']">Body part name should contain only alphabets.</div>
                      <div *ngIf="createTitleInput.errors?.['maxlength']">Body part name cannot exceed 30 characters.</div>
                  </div>
                  <div class="text-muted small mt-1" *ngIf="newBodyPartData.title">
                    {{newBodyPartData.title.length}}/30 characters
                  </div>
              </div>

              <!-- Enabled Status (Default to Active/true) -->
              <div class="col-12">
                  <label class="form-label d-block mb-2">Status</label>
                  <app-switch
                        [(checked)]="newBodyPartData.enabled"
                        name="enabled"
                        onLabel="Active"
                        offLabel="Inactive">
                  </app-switch>
              </div>

              <!-- Action Buttons -->
              <div class="col-12 mt-4 d-flex justify-content-end gap-2">
                  <button type="button" class="btn btn-secondary" (click)="closeCreateModal()">
                      Cancel
                  </button>
                  <button type="submit" class="btn adani-btn" [disabled]="createForm.invalid || createLoading">
                      <span *ngIf="!createLoading"><i class="bi bi-plus-circle-fill me-1"></i> Create Body Part</span>
                      <span *ngIf="createLoading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                      <span *ngIf="createLoading"> Creating...</span>
                  </button>
              </div>
          </div>
      </form>
  </div>
</app-offcanvas>
<!-- ********** END: Create Offcanvas ********** -->
