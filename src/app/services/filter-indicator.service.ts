import { Injectable } from '@angular/core';

/**
 * Service to manage filter state detection and provide utilities for determining
 * when filters are active vs default state across the application.
 */
@Injectable({
  providedIn: 'root'
})
export class FilterIndicatorService {

  constructor() { }

  /**
   * Determines if any filters are currently active (not in default state)
   * @param currentFilters - The current filter object
   * @param defaultFilters - The default filter object (what resetFilters() sets)
   * @returns boolean indicating if any filters are active
   */
  hasActiveFilters(currentFilters: any, defaultFilters: any): boolean {
    if (!currentFilters || !defaultFilters) {
      return false;
    }

    // Compare each filter property
    for (const key in currentFilters) {
      if (currentFilters.hasOwnProperty(key)) {
        const currentValue = currentFilters[key];
        const defaultValue = defaultFilters[key];

        // Skip comparison for certain system fields that don't represent user filters
        if (this.isSystemField(key)) {
          continue;
        }

        // Check if the current value differs from default
        if (!this.valuesEqual(currentValue, defaultValue)) {
          return true;
        }
      }
    }

    return false;
  }

  /**
   * Gets the count of active filters
   * @param currentFilters - The current filter object
   * @param defaultFilters - The default filter object
   * @returns number of active filters
   */
  getActiveFilterCount(currentFilters: any, defaultFilters: any): number {
    if (!currentFilters || !defaultFilters) {
      return 0;
    }

    let count = 0;
    for (const key in currentFilters) {
      if (currentFilters.hasOwnProperty(key)) {
        const currentValue = currentFilters[key];
        const defaultValue = defaultFilters[key];

        // Skip system fields
        if (this.isSystemField(key)) {
          continue;
        }

        // Check if the current value differs from default
        if (!this.valuesEqual(currentValue, defaultValue)) {
          count++;
        }
      }
    }

    return count;
  }

  /**
   * Checks if a field is a system field that shouldn't be considered for filter indicators
   * @param fieldName - The field name to check
   * @returns boolean indicating if it's a system field
   */
  private isSystemField(fieldName: string): boolean {
    const systemFields = [
      'sortField',
      'sortDirection',
      'sort',
      'id', // Usually used for role-based filtering, not user-applied filters
    ];
    return systemFields.includes(fieldName);
  }

  /**
   * Compares two values for equality, handling different data types appropriately
   * @param value1 - First value to compare
   * @param value2 - Second value to compare
   * @returns boolean indicating if values are equal
   */
  private valuesEqual(value1: any, value2: any): boolean {
    // Handle null/undefined cases
    if (value1 === null && value2 === null) return true;
    if (value1 === undefined && value2 === undefined) return true;
    if (value1 === null && value2 === undefined) return true;
    if (value1 === undefined && value2 === null) return true;
    
    // Handle empty string cases (treat as null/undefined)
    if ((value1 === '' || value1 === null || value1 === undefined) && 
        (value2 === '' || value2 === null || value2 === undefined)) {
      return true;
    }

    // Handle array comparison
    if (Array.isArray(value1) && Array.isArray(value2)) {
      if (value1.length !== value2.length) return false;
      return value1.every((item, index) => item === value2[index]);
    }

    // Handle array vs null/empty cases
    if (Array.isArray(value1) && (value2 === null || value2 === undefined)) {
      return value1.length === 0;
    }
    if (Array.isArray(value2) && (value1 === null || value1 === undefined)) {
      return value2.length === 0;
    }

    // Standard equality check
    return value1 === value2;
  }

  /**
   * Creates a default filter object based on common patterns found in the application
   * This is a helper method for components that don't have explicit default filters
   * @param filterObject - The current filter object to analyze
   * @returns A default filter object with common defaults
   */
  createDefaultFilters(filterObject: any): any {
    const defaults: any = {};
    
    for (const key in filterObject) {
      if (filterObject.hasOwnProperty(key)) {
        // Set common defaults based on field patterns
        if (key.includes('Name') || key.includes('Title') || key.includes('Description') || 
            key === 'firstName' || key === 'lastName' || key === 'email' || key === 'mobile' || 
            key === 'contactNumber' || key === 'name' || key === 'shortDescription') {
          defaults[key] = null;
        } else if (key.includes('Date') || key === 'startDate' || key === 'endDate' || 
                   key === 'fromDate' || key === 'toDate') {
          defaults[key] = null;
        } else if (key.includes('Id') && key !== 'sortField') {
          defaults[key] = null;
        } else if (key === 'enabled') {
          // Most components default enabled to 'true' or true
          defaults[key] = typeof filterObject[key] === 'boolean' ? true : 'true';
        } else if (key === 'sortField') {
          defaults[key] = 'id';
        } else if (key === 'sortDirection') {
          defaults[key] = 'DESC';
        } else if (key === 'sort') {
          defaults[key] = 'id,DESC';
        } else if (key === 'year') {
          defaults[key] = new Date().getFullYear();
        } else {
          // Default to null for unknown fields
          defaults[key] = null;
        }
      }
    }

    return defaults;
  }

  /**
   * Utility method to get a list of active filter names for debugging or display
   * @param currentFilters - The current filter object
   * @param defaultFilters - The default filter object
   * @returns Array of active filter field names
   */
  getActiveFilterNames(currentFilters: any, defaultFilters: any): string[] {
    if (!currentFilters || !defaultFilters) {
      return [];
    }

    const activeFilters: string[] = [];
    for (const key in currentFilters) {
      if (currentFilters.hasOwnProperty(key)) {
        const currentValue = currentFilters[key];
        const defaultValue = defaultFilters[key];

        // Skip system fields
        if (this.isSystemField(key)) {
          continue;
        }

        // Check if the current value differs from default
        if (!this.valuesEqual(currentValue, defaultValue)) {
          activeFilters.push(key);
        }
      }
    }

    return activeFilters;
  }
}
