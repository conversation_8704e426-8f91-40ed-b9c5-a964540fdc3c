<div class="filter-indicator-wrapper position-relative d-inline-block">
  <!-- Filter Button -->
  <img 
    src="../../../assets/svg/filter.svg" 
    [class]="getFilterButtonClasses()"
    [ngStyle]="getFilterButtonStyles()"
    [alt]="altText"
    [title]="getTooltipText()"
    (click)="onFilterClick()"
  />
  
  <!-- Active Filter Indicator -->
  <div 
    *ngIf="hasActiveFilters" 
    class="filter-indicator-badge"
    [class.with-count]="showCount && activeFilterCount > 0"
  >
    <span *ngIf="showCount && activeFilterCount > 0" class="filter-count">
      {{ activeFilterCount > 99 ? '99+' : activeFilterCount }}
    </span>
  </div>
</div>
