@import '../../../assets/scss/variables.scss';

.filter-indicator-wrapper {
  position: relative;
  display: inline-block;
}

.filter-button {
  transition: all 0.2s ease-in-out;
  
  &:hover {
    opacity: 0.8;
    transform: scale(1.05);
  }
  
  &:active {
    transform: scale(0.95);
  }
}

.filter-indicator-badge {
  position: absolute;
  top: -6px;
  right: -6px;
  min-width: 18px;
  height: 18px;
  background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
  border: 2px solid #ffffff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  z-index: 10;
  
  // Animation for when the badge appears
  animation: badgeAppear 0.3s ease-out;
  
  // When showing count, make it slightly larger and more oval
  &.with-count {
    min-width: 20px;
    height: 20px;
    border-radius: 10px;
    padding: 0 4px;
    
    // For larger counts, make it more oval
    &:has(.filter-count:not(:empty)) {
      border-radius: 12px;
    }
  }
  
  .filter-count {
    color: #ffffff;
    font-size: 10px;
    font-weight: 700;
    line-height: 1;
    text-align: center;
    min-width: 12px;
    
    // Ensure text is always visible
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  }
}

// Alternative color schemes for different contexts
.filter-indicator-wrapper {
  // Primary theme variant
  &.theme-primary .filter-indicator-badge {
    background: linear-gradient(135deg, $theme1-primary 0%, darken($theme1-primary, 10%) 100%);
  }
  
  // Success theme variant
  &.theme-success .filter-indicator-badge {
    background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
  }
  
  // Warning theme variant
  &.theme-warning .filter-indicator-badge {
    background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
    
    .filter-count {
      color: #212529; // Dark text for better contrast on yellow
      text-shadow: 0 1px 2px rgba(255, 255, 255, 0.3);
    }
  }
  
  // Info theme variant
  &.theme-info .filter-indicator-badge {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
  }
}

// Keyframe animation for badge appearance
@keyframes badgeAppear {
  0% {
    opacity: 0;
    transform: scale(0.5);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .filter-indicator-badge {
    min-width: 16px;
    height: 16px;
    top: -4px;
    right: -4px;
    
    &.with-count {
      min-width: 18px;
      height: 18px;
    }
    
    .filter-count {
      font-size: 9px;
    }
  }
}

// High contrast mode support
@media (prefers-contrast: high) {
  .filter-indicator-badge {
    border-width: 3px;
    box-shadow: 0 0 0 1px #000000, 0 2px 4px rgba(0, 0, 0, 0.4);
    
    .filter-count {
      font-weight: 900;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
    }
  }
}

// Reduced motion support
@media (prefers-reduced-motion: reduce) {
  .filter-button {
    transition: none;
    
    &:hover {
      transform: none;
    }
    
    &:active {
      transform: none;
    }
  }
  
  .filter-indicator-badge {
    animation: none;
  }
}

// Print styles
@media print {
  .filter-indicator-badge {
    display: none;
  }
}
