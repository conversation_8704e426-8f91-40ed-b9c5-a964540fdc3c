import { Component, Input, Output, EventEmitter, OnInit, OnChanges, SimpleChanges } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FilterIndicatorService } from '../../services/filter-indicator.service';

/**
 * Reusable component that wraps filter buttons and shows visual indicators when filters are active.
 * This component provides a consistent visual indicator across all filter implementations in the application.
 */
@Component({
  selector: 'app-filter-indicator',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './filter-indicator.component.html',
  styleUrls: ['./filter-indicator.component.scss']
})
export class FilterIndicatorComponent implements OnInit, OnChanges {

  /**
   * Current filter object from the parent component
   */
  @Input() currentFilters: any = {};

  /**
   * Default filter object (what resetFilters() would set)
   * If not provided, the service will attempt to create defaults
   */
  @Input() defaultFilters: any = {};

  /**
   * Custom title for the filter button tooltip
   */
  @Input() title: string = 'Filter';

  /**
   * Custom alt text for the filter icon
   */
  @Input() altText: string = 'Filter';

  /**
   * Whether to show the count of active filters in the indicator
   */
  @Input() showCount: boolean = true;

  /**
   * Custom CSS classes to apply to the filter button
   */
  @Input() customClasses: string = '';

  /**
   * Custom inline styles to apply to the filter button
   */
  @Input() customStyles: { [key: string]: string } = {};

  /**
   * Event emitted when the filter button is clicked
   */
  @Output() filterClick = new EventEmitter<void>();

  /**
   * Whether any filters are currently active
   */
  hasActiveFilters: boolean = false;

  /**
   * Number of active filters
   */
  activeFilterCount: number = 0;

  /**
   * Default styles for the filter button
   */
  defaultStyles = {
    width: '35px',
    cursor: 'pointer'
  };

  constructor(private filterIndicatorService: FilterIndicatorService) { }

  ngOnInit(): void {
    this.updateFilterState();
  }

  ngOnChanges(changes: SimpleChanges): void {
    // Update filter state when inputs change
    if (changes['currentFilters'] || changes['defaultFilters']) {
      this.updateFilterState();
    }
  }

  /**
   * Updates the internal state based on current and default filters
   */
  private updateFilterState(): void {
    let defaultsToUse = this.defaultFilters;

    // If no default filters provided, try to create them
    if (!defaultsToUse || Object.keys(defaultsToUse).length === 0) {
      defaultsToUse = this.filterIndicatorService.createDefaultFilters(this.currentFilters);
    }

    this.hasActiveFilters = this.filterIndicatorService.hasActiveFilters(this.currentFilters, defaultsToUse);
    this.activeFilterCount = this.filterIndicatorService.getActiveFilterCount(this.currentFilters, defaultsToUse);
  }

  /**
   * Handles the filter button click
   */
  onFilterClick(): void {
    this.filterClick.emit();
  }

  /**
   * Gets the combined styles for the filter button
   */
  getFilterButtonStyles(): { [key: string]: string } {
    return { ...this.defaultStyles, ...this.customStyles };
  }

  /**
   * Gets the combined CSS classes for the filter button
   */
  getFilterButtonClasses(): string {
    const baseClasses = 'filter-button ms-3';
    return this.customClasses ? `${baseClasses} ${this.customClasses}` : baseClasses;
  }

  /**
   * Gets the tooltip text for the filter button
   */
  getTooltipText(): string {
    if (this.hasActiveFilters && this.showCount) {
      const filterText = this.activeFilterCount === 1 ? 'filter' : 'filters';
      return `${this.title} (${this.activeFilterCount} active ${filterText})`;
    }
    return this.title;
  }
}
